#include "my_application.h"

#include <flutter_linux/flutter_linux.h>
#ifdef GDK_WINDOWING_X11
#include <gdk/gdkx.h>
#include <X11/Xlib.h>
#endif

#include "flutter/generated_plugin_registrant.h"

struct _MyApplication {
  GtkApplication parent_instance;
  char** dart_entrypoint_arguments;
};

G_DEFINE_TYPE(MyApplication, my_application, GTK_TYPE_APPLICATION)

// Function to position cursor using X11
static void position_cursor(int x, int y) {
#ifdef GDK_WINDOWING_X11
  GdkDisplay* display = gdk_display_get_default();
  if (GDK_IS_X11_DISPLAY(display)) {
    Display* x_display = gdk_x11_display_get_xdisplay(display);
    if (x_display) {
      XWarpPointer(x_display, None, DefaultRootWindow(x_display), 0, 0, 0, 0, x, y);
      XFlush(x_display);
    }
  }
#endif
}

// Method channel handler for cursor positioning
static void cursor_method_call_handler(FlMethodChannel* channel,
                                       FlMethodCall* method_call,
                                       gpointer user_data) {
  const gchar* method = fl_method_call_get_name(method_call);

  if (strcmp(method, "positionCursor") == 0) {
    FlValue* args = fl_method_call_get_args(method_call);
    if (fl_value_get_type(args) == FL_VALUE_TYPE_MAP) {
      FlValue* x_value = fl_value_lookup_string(args, "x");
      FlValue* y_value = fl_value_lookup_string(args, "y");

      if (x_value && y_value &&
          fl_value_get_type(x_value) == FL_VALUE_TYPE_INT &&
          fl_value_get_type(y_value) == FL_VALUE_TYPE_INT) {
        int x = fl_value_get_int(x_value);
        int y = fl_value_get_int(y_value);

        position_cursor(x, y);

        g_autoptr(FlValue) result = fl_value_new_bool(TRUE);
        fl_method_call_respond_success(method_call, result, nullptr);
      } else {
        fl_method_call_respond_error(method_call, "INVALID_ARGUMENTS",
                                     "Missing or invalid x/y coordinates", nullptr, nullptr);
      }
    } else {
      fl_method_call_respond_error(method_call, "INVALID_ARGUMENTS",
                                   "Arguments must be a map", nullptr, nullptr);
    }
  } else {
    fl_method_call_respond_not_implemented(method_call, nullptr);
  }
}

// Setup cursor method channel
static void setup_cursor_method_channel(FlView* view) {
  g_autoptr(FlStandardMethodCodec) codec = fl_standard_method_codec_new();
  g_autoptr(FlMethodChannel) channel = fl_method_channel_new(
      fl_engine_get_binary_messenger(fl_view_get_engine(view)),
      "com.app.signage/cursor",
      FL_METHOD_CODEC(codec));

  fl_method_channel_set_method_call_handler(channel, cursor_method_call_handler, nullptr, nullptr);
}

// Implements GApplication::activate.
static void my_application_activate(GApplication* application) {
  MyApplication* self = MY_APPLICATION(application);
  GtkWindow* window =
      GTK_WINDOW(gtk_application_window_new(GTK_APPLICATION(application)));

  // Use a header bar when running in GNOME as this is the common style used
  // by applications and is the setup most users will be using (e.g. Ubuntu
  // desktop).
  // If running on X and not using GNOME then just use a traditional title bar
  // in case the window manager does more exotic layout, e.g. tiling.
  // If running on Wayland assume the header bar will work (may need changing
  // if future cases occur).
  gboolean use_header_bar = TRUE;
#ifdef GDK_WINDOWING_X11
  GdkScreen* screen = gtk_window_get_screen(window);
  if (GDK_IS_X11_SCREEN(screen)) {
    const gchar* wm_name = gdk_x11_screen_get_window_manager_name(screen);
    if (g_strcmp0(wm_name, "GNOME Shell") != 0) {
      use_header_bar = FALSE;
    }
  }
#endif
  if (use_header_bar) {
    GtkHeaderBar* header_bar = GTK_HEADER_BAR(gtk_header_bar_new());
    gtk_widget_show(GTK_WIDGET(header_bar));
    gtk_header_bar_set_title(header_bar, "signage");
    gtk_header_bar_set_show_close_button(header_bar, TRUE);
    gtk_window_set_titlebar(window, GTK_WIDGET(header_bar));
  } else {
    gtk_window_set_title(window, "signage");
  }

  // Set window to fullscreen mode across all monitors
  gtk_window_set_decorated(window, FALSE);

  // Get all monitors
  GdkDisplay* display = gdk_display_get_default();
  int num_monitors = gdk_display_get_n_monitors(display);

  // Calculate total width and height
  int total_width = 0;
  int total_height = 0;

  for (int i = 0; i < num_monitors; i++) {
    GdkMonitor* monitor = gdk_display_get_monitor(display, i);
    GdkRectangle geometry;
    gdk_monitor_get_geometry(monitor, &geometry);

    // Apply the algorithm as specified
    if (geometry.width > geometry.height) {
      // Landscape orientation
      total_width += geometry.width;
      if (geometry.height > total_height) {
        total_height = geometry.height;
      }
    } else if (geometry.width < geometry.height) {
      // Portrait orientation
      if (geometry.width > total_width) {
        total_width = geometry.width;
      }
      total_height += geometry.height;
    } else {
      // Square monitor
      if (geometry.width > total_width) {
        total_width = geometry.width;
      }
      if (geometry.height > total_height) {
        total_height = geometry.height;
      }
    }

    g_print("Monitor %d: %dx%d\n", i, geometry.width, geometry.height);
  }

  g_print("Total dimensions: %dx%d\n", total_width, total_height);

  // Set window size to cover all monitors
  if (total_width > 0 && total_height > 0) {
    gtk_window_set_default_size(window, total_width, total_height);
    // Position at (0,0)
    gtk_window_move(window, 0, 0);
  } else {
    // Fallback to default size
    gtk_window_set_default_size(window, 1920, 1080);
  }

  gtk_window_maximize(window);
  gtk_window_fullscreen(window);
  gtk_widget_show(GTK_WIDGET(window));

  g_autoptr(FlDartProject) project = fl_dart_project_new();
  fl_dart_project_set_dart_entrypoint_arguments(project, self->dart_entrypoint_arguments);

  FlView* view = fl_view_new(project);
  gtk_widget_show(GTK_WIDGET(view));
  gtk_container_add(GTK_CONTAINER(window), GTK_WIDGET(view));

  fl_register_plugins(FL_PLUGIN_REGISTRY(view));

  // Setup cursor method channel
  setup_cursor_method_channel(view);

  gtk_widget_grab_focus(GTK_WIDGET(view));
}

// Implements GApplication::local_command_line.
static gboolean my_application_local_command_line(GApplication* application, gchar*** arguments, int* exit_status) {
  MyApplication* self = MY_APPLICATION(application);
  // Strip out the first argument as it is the binary name.
  self->dart_entrypoint_arguments = g_strdupv(*arguments + 1);

  g_autoptr(GError) error = nullptr;
  if (!g_application_register(application, nullptr, &error)) {
     g_warning("Failed to register: %s", error->message);
     *exit_status = 1;
     return TRUE;
  }

  g_application_activate(application);
  *exit_status = 0;

  return TRUE;
}

// Implements GApplication::startup.
static void my_application_startup(GApplication* application) {
  //MyApplication* self = MY_APPLICATION(object);

  // Perform any actions required at application startup.

  G_APPLICATION_CLASS(my_application_parent_class)->startup(application);
}

// Implements GApplication::shutdown.
static void my_application_shutdown(GApplication* application) {
  //MyApplication* self = MY_APPLICATION(object);

  // Perform any actions required at application shutdown.

  G_APPLICATION_CLASS(my_application_parent_class)->shutdown(application);
}

// Implements GObject::dispose.
static void my_application_dispose(GObject* object) {
  MyApplication* self = MY_APPLICATION(object);
  g_clear_pointer(&self->dart_entrypoint_arguments, g_strfreev);
  G_OBJECT_CLASS(my_application_parent_class)->dispose(object);
}

static void my_application_class_init(MyApplicationClass* klass) {
  G_APPLICATION_CLASS(klass)->activate = my_application_activate;
  G_APPLICATION_CLASS(klass)->local_command_line = my_application_local_command_line;
  G_APPLICATION_CLASS(klass)->startup = my_application_startup;
  G_APPLICATION_CLASS(klass)->shutdown = my_application_shutdown;
  G_OBJECT_CLASS(klass)->dispose = my_application_dispose;
}

static void my_application_init(MyApplication* self) {}

MyApplication* my_application_new() {
  // Set the program name to the application ID, which helps various systems
  // like GTK and desktop environments map this running application to its
  // corresponding .desktop file. This ensures better integration by allowing
  // the application to be recognized beyond its binary name.
  g_set_prgname(APPLICATION_ID);

  return MY_APPLICATION(g_object_new(my_application_get_type(),
                                     "application-id", APPLICATION_ID,
                                     "flags", G_APPLICATION_NON_UNIQUE,
                                     nullptr));
}
